<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能地点查询功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f4f8 100%);
            margin: 0;
            padding: 20px;
            color: #2c3e50;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: rgba(227, 242, 253, 0.5);
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #64b5f6;
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .demo-section {
            background: rgba(255, 255, 255, 0.8);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(100, 181, 246, 0.2);
        }
        .demo-input {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        .demo-output {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #66bb6a;
        }
        .code-block {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .highlight {
            background: rgba(100, 181, 246, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .tech-tag {
            background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能地点查询功能演示</h1>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎯 AI地点识别</h3>
                <p>使用DeepSeek模型解析自然语言输入，准确识别校园地点名称，支持模糊描述和多种表达方式。</p>
            </div>
            <div class="feature-card">
                <h3>📍 智能距离计算</h3>
                <p>基于欧几里得距离算法，自动计算到最近招领点的距离，提供精确的位置信息。</p>
            </div>
            <div class="feature-card">
                <h3>💬 友好建议生成</h3>
                <p>AI生成个性化的路线建议和实用提示，让用户轻松找到目标招领点。</p>
            </div>
        </div>

        <div class="demo-section">
            <h2>📝 功能演示示例</h2>
            
            <h3>示例1：模糊地点描述</h3>
            <div class="demo-input">
                <strong>用户输入：</strong>"我在图书馆丢了钱包，请问最近的招领点在哪里？"
            </div>
            <div class="demo-output">
                <strong>AI解析结果：</strong>
                <ul>
                    <li>识别地点：<span class="highlight">图书馆</span></li>
                    <li>置信度：<span class="highlight">95%</span></li>
                    <li>最近招领点：<span class="highlight">图书馆招领点</span></li>
                    <li>距离：<span class="highlight">25个单位</span></li>
                    <li>AI建议：图书馆招领点就在图书馆内部，建议您直接前往一楼服务台咨询...</li>
                </ul>
            </div>

            <h3>示例2：建筑物查询</h3>
            <div class="demo-input">
                <strong>用户输入：</strong>"教一楼附近有招领点吗？"
            </div>
            <div class="demo-output">
                <strong>AI解析结果：</strong>
                <ul>
                    <li>识别地点：<span class="highlight">教一楼</span></li>
                    <li>置信度：<span class="highlight">90%</span></li>
                    <li>最近招领点：<span class="highlight">中一楼招领点</span></li>
                    <li>距离：<span class="highlight">156.2个单位</span></li>
                    <li>AI建议：从教一楼到中一楼招领点需要步行约5分钟，建议沿着主干道前往...</li>
                </ul>
            </div>

            <h3>示例3：宿舍区查询</h3>
            <div class="demo-input">
                <strong>用户输入：</strong>"梧桐苑最近的失物招领处"
            </div>
            <div class="demo-output">
                <strong>AI解析结果：</strong>
                <ul>
                    <li>识别地点：<span class="highlight">梧桐苑</span></li>
                    <li>置信度：<span class="highlight">98%</span></li>
                    <li>最近招领点：<span class="highlight">梧桐苑招领点</span></li>
                    <li>距离：<span class="highlight">7个单位</span></li>
                    <li>AI建议：梧桐苑招领点就在宿舍楼下，非常方便，开放时间为每天8:00-22:00...</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ 技术实现</h2>
            
            <h3>技术栈</h3>
            <div class="tech-stack">
                <span class="tech-tag">Python Flask</span>
                <span class="tech-tag">DeepSeek AI</span>
                <span class="tech-tag">JavaScript</span>
                <span class="tech-tag">HTML5/CSS3</span>
                <span class="tech-tag">SQLite</span>
                <span class="tech-tag">JSON数据库</span>
            </div>

            <h3>核心算法</h3>
            <div class="code-block">
def find_nearest_lost_and_found(location_name):
    # 1. 查找目标地点坐标
    target_location = find_location_by_name(location_name)
    
    # 2. 计算到所有招领点的距离
    distances = []
    for point in lost_and_found_points:
        distance = sqrt((x2-x1)² + (y2-y1)²)
        distances.append((point, distance))
    
    # 3. 返回最近的招领点
    return min(distances, key=lambda x: x[1])
            </div>

            <h3>API接口</h3>
            <div class="code-block">
POST /smart_location_query
{
    "user_input": "我在图书馆丢了钱包"
}

Response:
{
    "success": true,
    "parsing_result": {
        "found_locations": ["图书馆"],
        "confidence": 0.95,
        "reasoning": "AI成功识别地点"
    },
    "results": [{
        "location": "图书馆",
        "nearest_lost_and_found": {
            "name": "图书馆",
            "distance": 25.0,
            "coordinates": {"x": 619, "y": 768}
        },
        "ai_advice": "建议前往图书馆一楼服务台..."
    }]
}
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 使用方法</h2>
            <ol>
                <li>确保已安装Python和必要依赖：<code>pip install flask flask-sqlalchemy requests</code></li>
                <li>配置DeepSeek API密钥在app.py中</li>
                <li>运行启动脚本：<code>python start_app.py</code></li>
                <li>访问 <code>http://localhost:5000</code></li>
                <li>注册/登录后点击"智能地点查询"模块</li>
                <li>输入地点描述，享受AI智能查询服务</li>
            </ol>
        </div>

        <div class="demo-section">
            <h2>✨ 特色功能</h2>
            <ul>
                <li><strong>自然语言处理：</strong>支持各种表达方式，无需记忆精确地点名称</li>
                <li><strong>智能容错：</strong>API失败时自动降级到关键词匹配</li>
                <li><strong>实时反馈：</strong>查询状态实时显示，用户体验流畅</li>
                <li><strong>响应式设计：</strong>完美适配桌面和移动设备</li>
                <li><strong>一致性设计：</strong>与现有系统风格完全融合</li>
            </ul>
        </div>
    </div>
</body>
</html>
