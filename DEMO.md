# 校园卡管理系统演示指南

## 系统概述

这是一个完整的校园卡管理系统，包含用户注册登录、校园卡报告查询、论坛交流、积分奖励等功能。

## 演示流程

### 1. 启动系统

```bash
python start.py
```

系统会自动打开浏览器并显示注册页面。

### 2. 用户注册

在注册页面填写：
- 学号：任意数字（如：2021006）
- 姓名：你的姓名
- 密码：任意密码

点击"注册"按钮完成注册。

### 3. 用户登录

注册成功后会跳转到登录页面，使用刚才注册的信息登录。

或者使用预置的测试账号：
- 学号：2021001，姓名：张三，密码：123456

### 4. 功能演示

登录后进入首页，可以体验以下功能：

#### 4.1 报告捡到校园卡
- 点击"报告捡到校园卡"
- 填写校园卡号（如：2021007）
- 填写发现地点（如：图书馆三楼）
- 选择处理方式（自行联系或放置指定地点）
- **新功能**: 可以上传校园卡照片（支持jpg、png、gif格式，最大16MB）
- 上传后会显示图片预览，可以移除重新选择
- 提交报告

#### 4.2 查询丢失的校园卡
- 点击"查询丢失的校园卡"
- 输入学号查询（可以试试：2021004 或 2021005）
- 查看查询结果
- **新功能**:
  - 可以连续查询多个学号，无需退出页面
  - 自动保存查询历史，点击历史记录可快速重新查询
  - 支持键盘快捷键：Escape清空，Ctrl+Enter快速查询
  - 实时状态提示和结果高亮显示

#### 4.3 公示列表
- 点击"公示列表"
- **新功能**: 查看热门校园卡发现地点统计
  - 系统自动分析校园卡数据库中的found_location字段
  - 显示最热门的前10个校园卡发现地点
  - **多视图展示**: 支持卡片视图、柱状图、饼图三种展示方式
  - 前三名有特殊的排名标识（金银铜色）
  - 显示发现次数和占比百分比
  - **交互式图表**: 图表支持点击交互，可查看地点详情
  - **实时同步**: 所有视图数据保持同步更新
- 查看所有未认领的校园卡信息

#### 4.4 论坛功能
- 点击"论坛"查看现有帖子
- 点击"发布帖子"创建新帖子
- 可以选择是否标记为广告

#### 4.5 奖励兑换
- 点击"奖励兑换"
- 查看可兑换的奖品
- 使用积分兑换奖品（测试账号有不同的积分）

## 技术特点

### 前端特性
- 响应式设计，支持移动端
- 单页应用，流畅的页面切换
- 现代化的UI设计
- 原生JavaScript，无框架依赖
- 图片上传和预览功能
- 文件类型和大小验证

### 后端特性
- RESTful API设计
- SQLite数据库，轻量级部署
- 数据脱敏处理
- 完整的CRUD操作
- 文件上传处理
- 图片存储和访问

### 数据安全
- 用户信息脱敏显示
- 基本的输入验证
- 错误处理机制

## 系统架构

```
前端 (HTML+CSS+JS)
    ↓ HTTP请求
后端 (Flask API)
    ↓ ORM
数据库 (SQLite)
```

## 数据模型

- **User**: 用户信息（学号、姓名、密码、积分）
- **CampusCard**: 校园卡信息（卡号、状态、发现地点等）
- **ForumPost**: 论坛帖子（标题、内容、作者、时间）
- **Reward**: 奖励信息（名称、描述、所需积分）

## 扩展建议

1. **安全增强**
   - 密码加密存储
   - JWT身份验证
   - 输入验证和防XSS

2. **功能扩展**
   - 图片上传功能
   - 消息通知系统
   - 管理员后台

3. **性能优化**
   - 数据库索引优化
   - 缓存机制
   - 分页加载

4. **部署优化**
   - Docker容器化
   - 生产环境配置
   - 负载均衡

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python环境
   - 确认依赖已安装
   - 查看错误日志

2. **前端无法连接后端**
   - 确认后端服务正在运行
   - 检查端口是否被占用
   - 查看浏览器控制台错误

3. **数据库错误**
   - 删除 `campus_card.db` 文件
   - 重新运行 `python init_data.py`

### 调试技巧

1. 打开浏览器开发者工具查看网络请求
2. 查看后端控制台输出
3. 检查数据库文件是否正确生成

## 联系支持

如果遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 端口5000是否被占用
