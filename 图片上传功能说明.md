# 图片上传功能说明

## 功能概述

在"报告捡到校园卡"功能中，现在支持从本地直接上传图片，而不需要手动输入图片URL。

## 新增功能特性

### 1. 文件选择器
- 替换了原来的"照片链接"输入框
- 现在是"上传照片"文件选择器
- 支持点击选择或拖拽上传

### 2. 图片预览
- 选择图片后立即显示预览
- 显示文件名和文件大小
- 提供"移除图片"按钮

### 3. 文件验证
- **文件类型**: 仅支持图片文件 (jpg, png, gif, jpeg)
- **文件大小**: 最大16MB
- **错误提示**: 不符合要求时会弹出提示

### 4. 自动存储
- 图片自动上传到服务器的 `uploads/` 目录
- 生成唯一文件名防止冲突
- 自动转换为URL格式存储到数据库

## 技术实现

### 前端改动
1. **HTML**: 
   - 将 `<input type="url">` 改为 `<input type="file" accept="image/*">`
   - 添加图片预览容器

2. **CSS**: 
   - 美化文件上传样式
   - 添加图片预览样式
   - 响应式设计

3. **JavaScript**: 
   - 图片预览功能
   - 文件验证逻辑
   - FormData上传处理

### 后端改动
1. **新增接口**:
   - `POST /upload_image` - 单独的图片上传接口
   - `GET /uploads/<filename>` - 图片访问接口

2. **修改接口**:
   - `POST /report_card` - 支持multipart/form-data格式
   - 同时兼容JSON格式（向后兼容）

3. **文件处理**:
   - 自动创建uploads目录
   - 生成UUID文件名
   - 文件类型验证
   - 文件大小限制

## 使用方法

### 1. 启动系统
```bash
python start.py
```

### 2. 测试图片上传
1. 登录系统
2. 点击"报告捡到校园卡"
3. 填写基本信息
4. 点击"上传照片"选择图片文件
5. 查看图片预览
6. 提交表单

### 3. 验证功能
- 尝试上传不同格式的图片
- 尝试上传过大的文件
- 查看预览效果
- 提交后检查数据库中的photo_url字段

## 文件结构

```
uploads/
├── abc123def456.jpg    # 上传的图片文件
├── 789xyz012.png       # UUID命名，防止冲突
└── ...
```

## API接口

### 上传图片
```
POST /upload_image
Content-Type: multipart/form-data

参数:
- file: 图片文件

返回:
{
    "message": "File uploaded successfully",
    "file_url": "/uploads/abc123def456.jpg"
}
```

### 报告校园卡（支持图片）
```
POST /report_card
Content-Type: multipart/form-data

参数:
- card_number: 校园卡号
- found_location: 发现地点
- handler_option: 处理方式 (1或2)
- photo: 图片文件 (可选)

返回:
{
    "message": "Card reported successfully"
}
```

### 访问图片
```
GET /uploads/<filename>

返回: 图片文件
```

## 安全考虑

1. **文件类型限制**: 只允许图片格式
2. **文件大小限制**: 最大16MB
3. **文件名安全**: 使用UUID生成唯一文件名
4. **路径安全**: 使用secure_filename处理文件名

## 扩展建议

1. **图片压缩**: 自动压缩大图片
2. **缩略图**: 生成缩略图提高加载速度
3. **云存储**: 集成阿里云OSS或腾讯云COS
4. **图片水印**: 添加系统水印
5. **批量上传**: 支持一次上传多张图片

## 故障排除

### 常见问题

1. **上传失败**
   - 检查uploads目录权限
   - 确认文件大小不超过16MB
   - 验证文件格式是否正确

2. **图片无法显示**
   - 检查uploads目录是否存在
   - 确认后端服务正常运行
   - 查看浏览器控制台错误

3. **预览不显示**
   - 检查浏览器是否支持FileReader API
   - 确认JavaScript没有错误
   - 验证文件是否为有效图片

### 调试方法

1. 打开浏览器开发者工具
2. 查看Network标签页的请求
3. 检查Console标签页的错误信息
4. 验证uploads目录中的文件
