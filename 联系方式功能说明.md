# 联系方式功能说明

## 功能概述

在报告捡到校园卡功能中，新增了联系方式管理功能。当用户选择"自行联系失主"时，需要提供联系方式（手机号），失主查询时可以看到联系方式进行联系。

## 新增功能特性

### 1. 智能表单控制
- **条件显示**: 只有选择"自行联系失主"时才显示联系方式输入框
- **必填验证**: 选择自行联系时，联系方式为必填项
- **格式验证**: 自动验证手机号格式（11位，1开头）
- **实时切换**: 切换处理方式时自动显示/隐藏联系方式字段

### 2. 数据存储优化
- **新增字段**: 在CampusCard模型中添加contact字段
- **条件存储**: 只有选择自行联系时才存储联系方式
- **数据安全**: 联系方式仅在必要时显示给失主

### 3. 查询结果优化
- **分类显示**: 根据处理方式显示不同的信息
- **联系方式**: 自行联系时显示拾卡者手机号
- **地点信息**: 放置地点时显示具体位置
- **视觉区分**: 使用不同颜色和图标区分两种情况

## 技术实现

### 数据模型变更
```python
class CampusCard(db.Model):
    # ... 其他字段
    contact = db.Column(db.String(20))  # 新增联系方式字段
```

### 前端表单控制
```javascript
function toggleContactField() {
    const handlerOption = document.querySelector('input[name="handler-option"]:checked');
    const contactField = document.getElementById('contact-field');
    
    if (handlerOption && handlerOption.value === '1') {
        contactField.style.display = 'block';
        contactInput.required = true;
    } else {
        contactField.style.display = 'none';
        contactInput.required = false;
    }
}
```

### 后端接口增强
```python
# 验证联系方式
if handler_option == 1 and not contact:
    return jsonify({'error': 'Contact information is required'}), 400

# 验证手机号格式
if contact and not re.match(r'^1[3-9]\d{9}$', contact):
    return jsonify({'error': 'Invalid phone number format'}), 400
```

## 用户界面

### 报告页面
```
处理方式:
○ 自行联系失主
○ 放置到指定地点

[当选择"自行联系失主"时显示]
┌─────────────────────────────────────┐
│ 您的联系方式 (手机号):               │
│ [___________________]               │
│ 选择"自行联系失主"时必须提供联系方式  │
└─────────────────────────────────────┘
```

### 查询结果页面

**自行联系情况:**
```
┌─────────────────────────────────────┐
│ 🎉 好消息！                         │
│ 您的校园卡已被拾到，请自行联系拾卡者  │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📞 拾卡者联系方式：              │ │
│ │ 13800138001                    │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**放置地点情况:**
```
┌─────────────────────────────────────┐
│ 🎉 好消息！                         │
│ 您的校园卡已放在图书馆一楼处，请前往领取│
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📍 领取地点：                    │ │
│ │ 图书馆一楼                      │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 数据流程

### 报告流程
1. 用户选择处理方式
2. 如果选择"自行联系"，显示联系方式输入框
3. 用户输入手机号，系统验证格式
4. 提交时验证必填项和格式
5. 后端存储联系方式到contact字段

### 查询流程
1. 失主输入学号查询
2. 系统查找对应的校园卡记录
3. 根据handler_option判断返回类型
4. 如果是自行联系(1)，返回contact字段
5. 如果是放置地点(2)，返回found_location字段
6. 前端根据handler_type显示不同样式

## API接口变更

### POST /report_card
**新增参数:**
- `contact`: 联系方式（当handler_option=1时必填）

**验证规则:**
- handler_option=1时，contact必填
- contact格式必须为11位手机号
- 手机号必须以1开头，第二位为3-9

### GET /query_lost_card
**返回格式变更:**

**自行联系时:**
```json
{
    "status": "found",
    "message": "您的校园卡已被拾到，请自行联系拾卡者",
    "contact_info": "13800138001",
    "handler_type": "contact"
}
```

**放置地点时:**
```json
{
    "status": "found", 
    "message": "您的校园卡已放在图书馆一楼处，请前往领取",
    "location_info": "图书馆一楼",
    "handler_type": "location"
}
```

## 安全考虑

### 数据保护
1. **最小化原则**: 只在必要时收集和显示联系方式
2. **访问控制**: 只有失主可以看到拾卡者联系方式
3. **格式验证**: 严格验证手机号格式，防止无效数据

### 隐私保护
1. **条件显示**: 联系方式只在自行联系模式下显示
2. **数据清理**: 切换处理方式时自动清空不相关数据
3. **用户控制**: 用户可以选择是否提供联系方式

## 测试用例

### 功能测试
1. **表单控制测试**:
   - 选择"自行联系"，验证联系方式字段显示
   - 选择"放置地点"，验证联系方式字段隐藏
   - 切换选项，验证字段状态正确变化

2. **验证测试**:
   - 选择自行联系但不填联系方式，验证错误提示
   - 输入无效手机号格式，验证格式错误提示
   - 输入有效手机号，验证提交成功

3. **查询测试**:
   - 查询自行联系的卡片，验证显示联系方式
   - 查询放置地点的卡片，验证显示地点信息
   - 验证不同情况下的样式和图标

### 边界测试
1. **数据边界**:
   - 测试各种手机号格式
   - 测试空值和特殊字符
   - 测试超长输入

2. **状态切换**:
   - 快速切换处理方式
   - 填写后切换选项
   - 表单重置功能

## 数据库迁移

### 迁移脚本
使用 `migrate_db.py` 脚本为现有数据库添加contact字段：

```bash
python migrate_db.py
```

### 手动迁移
如果需要手动迁移：

```sql
ALTER TABLE campus_card ADD COLUMN contact VARCHAR(20);
```

## 扩展建议

### 短期改进
1. **联系方式类型**: 支持微信号、QQ号等其他联系方式
2. **隐私设置**: 允许用户选择是否公开联系方式
3. **消息模板**: 提供标准的联系消息模板

### 长期规划
1. **站内消息**: 通过系统内消息功能联系，保护隐私
2. **匿名联系**: 通过系统转发消息，不直接暴露联系方式
3. **评价系统**: 失主可以对拾卡者进行评价和感谢
