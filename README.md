# 校园卡管理系统

一个基于Flask后端和HTML+JS+CSS前端的校园卡管理系统。

## 功能特性

- **用户注册/登录**: 使用学号、姓名、密码进行注册和登录
- **报告捡到校园卡**: 发现校园卡时可以报告并选择处理方式，支持上传照片和联系方式
- **查询丢失校园卡**: 查询自己的校园卡是否被找到，支持连续查询和查询历史
- **公示列表**: 查看所有未认领的校园卡，展示热门丢失地点统计（支持卡片、柱状图、饼图三种视图）
- **论坛功能**: 发布和查看帖子，支持广告标记
- **奖励兑换**: 使用积分兑换奖品

## 技术栈

### 后端
- Flask (Python Web框架)
- SQLAlchemy (ORM)
- SQLite (数据库)
- Flask-CORS (跨域支持)

### 前端
- HTML5
- CSS3 (响应式设计)
- JavaScript (原生JS)

## 安装和运行

### 快速启动 (推荐)

```bash
python start.py
```

这个脚本会自动：
- 检查依赖
- 初始化数据库
- 启动后端服务
- 打开前端页面

### 手动启动

#### 1. 安装依赖

```bash
pip install flask flask-sqlalchemy
```

或者使用requirements.txt:

```bash
pip install -r requirements.txt
```

**注意**: 如果你的环境中没有安装Flask相关包，请先安装。系统已经内置了基本的CORS支持。

#### 2. 初始化数据库和测试数据

```bash
python init_data.py
```

#### 3. 启动后端服务

```bash
python app.py
```

后端服务将在 `http://localhost:5000` 启动

#### 4. 打开前端页面

直接在浏览器中打开 `index.html` 文件，或者使用本地服务器：

```bash
# 使用Python内置服务器
python -m http.server 8000
```

然后访问 `http://localhost:8000`

## 测试账号

系统已预置以下测试账号：

- **学号**: 2021001, **姓名**: 张三, **密码**: 123456, **积分**: 100
- **学号**: 2021002, **姓名**: 李四, **密码**: 123456, **积分**: 50  
- **学号**: 2021003, **姓名**: 王五, **密码**: 123456, **积分**: 200

## API接口

### 用户相关
- `POST /register` - 用户注册
- `POST /login` - 用户登录

### 校园卡相关
- `POST /report_card` - 报告捡到校园卡 (支持图片上传)
- `GET /query_lost_card` - 查询丢失校园卡

### 图片相关
- `POST /upload_image` - 上传图片
- `GET /uploads/<filename>` - 获取上传的图片

### 论坛相关
- `POST /forum/post` - 创建帖子
- `GET /forum/posts` - 获取帖子列表
- `GET /hot_locations` - 获取热门校园卡发现地点统计

### 奖励相关
- `GET /rewards` - 获取奖励列表
- `POST /reward/redeem` - 兑换奖励

## 项目结构

```
├── app.py              # Flask应用主文件
├── models.py           # 数据模型定义
├── init_data.py        # 测试数据初始化
├── start.py            # 一键启动脚本
├── index.html          # 前端主页面
├── style.css           # 样式文件
├── script.js           # JavaScript逻辑
├── requirements.txt    # Python依赖
├── README.md           # 项目说明
├── DEMO.md             # 演示指南
├── uploads/            # 图片上传目录(运行后生成)
└── campus_card.db      # SQLite数据库文件(运行后生成)
```

## 使用流程

1. **注册账号**: 首次使用需要注册账号
2. **登录系统**: 使用学号、姓名、密码登录
3. **使用功能**: 
   - 捡到校园卡时使用"报告捡到校园卡"功能
   - 丢失校园卡时使用"查询丢失的校园卡"功能
   - 查看"公示列表"了解未认领的校园卡
   - 在"论坛"中交流讨论
   - 使用积分在"奖励兑换"中兑换奖品

## 注意事项

- 确保后端服务正常运行
- 前端页面需要能够访问后端API (注意CORS设置)
- 数据库文件会在首次运行时自动创建
- 建议在开发环境中使用，生产环境需要额外的安全配置
